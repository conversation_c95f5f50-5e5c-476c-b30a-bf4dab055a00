import React from 'react'
import NoResult from '../NoResult/NoResult'
import { useRouter } from 'next/navigation'

const greenTick = () => {
  return (
    <svg
      width="88"
      height="88"
      viewBox="0 0 88 88"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M43.9997 7.32812C23.7963 7.32812 7.33301 23.7915 7.33301 43.9948C7.33301 64.1981 23.7963 80.6615 43.9997 80.6615C64.203 80.6615 80.6663 64.1981 80.6663 43.9948C80.6663 23.7915 64.203 7.32812 43.9997 7.32812ZM61.5263 35.5615L40.7363 56.3515C40.223 56.8648 39.5263 57.1581 38.793 57.1581C38.0597 57.1581 37.363 56.8648 36.8497 56.3515L26.473 45.9748C25.4097 44.9115 25.4097 43.1515 26.473 42.0881C27.5363 41.0248 29.2963 41.0248 30.3597 42.0881L38.793 50.5215L57.6397 31.6748C58.703 30.6115 60.463 30.6115 61.5263 31.6748C62.5897 32.7381 62.5897 34.4615 61.5263 35.5615Z"
        fill="#01BD77"
      />
    </svg>
  )
}
const SuccessfullyFormSubmit = ({ formTitle }: { formTitle: string }) => {
  const router = useRouter()
  return (
    <div className="w-full my-56 flex justify-center items-center flex-col gap-6">
      <h1 className="font-semibold text-3xl"> {formTitle}</h1>
      <NoResult
        icon={greenTick()}
        title="Application Submitted"
        desc="Thank you for your interest in teaching at The Muse. Someone will be in touch with you soon!"
      />
    </div>
  )
}

export default SuccessfullyFormSubmit
