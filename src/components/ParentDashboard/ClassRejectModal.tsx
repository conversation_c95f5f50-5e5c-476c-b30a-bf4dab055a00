'use client'
import { useUpdateParentTodoMutation } from '@/lib/redux/slices/apiSlice/apiSlice'
import { X } from 'lucide-react'
import { useEffect } from 'react'
import { toast } from 'react-toastify'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { RecordType } from '../ClassRegistrationModal/ClassRegistrationModal'

const ClassRejectModal = ({
  onClose,
  item,
}: {
  onClose: () => void
  item: any
}) => {
  const [updateParentTodo, { isLoading, isSuccess, isError }] =
    useUpdateParentTodoMutation()
  const handleReject = async (orderId: string) => {
    const res = await updateParentTodo({
      isTokenRequired: true,
      orderId,
      type: 'Rejected',
    })
  }

  useEffect(() => {
    if (isSuccess) {
      toast.success('Class request rejected successfully', { autoClose: 2000 })
      onClose()
    }
  }, [isSuccess])

  const getDynamicTitle = (item: any) => {
    switch (item?.Type) {
      case RecordType.ClassRegistration:
        return 'Class Registration Request'
      case RecordType.FlexiblePayment:
        return 'Class Registration Request (Flexible Payment)'
      case RecordType.MembershipMonthly:
        return 'Membership Purchase Request'
      case RecordType.MembershipAnnual:
        return 'Membership Purchase Request'
      default:
        return 'Class Registration Request'
    }
  }

  const getDynamicDescription = (item: any) => {
    switch (item?.Type) {
      case RecordType.ClassRegistration:
        return `${item?.Account__r?.Name} has requested an approval to attend the following classes:`
      case RecordType.FlexiblePayment:
        return `${item?.Account__r?.Name} has requested an approval to attend the following classes(with flexible payment):`
      case RecordType.MembershipMonthly:
        return `${item?.Account__r?.Name} has requested an approval to purchase a monthly membership at The Muse:`
      case RecordType.MembershipAnnual:
        return `${item?.Account__r?.Name} has requested an approval to purchase a annual membership at The Muse:`
      default:
        return `${item?.Account__r?.Name} has requested an approval to purchase a membership at The Muse:`
    }
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50 p-3">
      <div className="bg-white rounded-2xl shadow-lg max-w-md w-full p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">{getDynamicTitle(item)}</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-color-grey-5 rounded-full transition-colors"
          >
            <X size={20} className="text-color-grey-1" />
          </button>
        </div>

        {/* Description */}
        <p className="font-normal text-sm text-color-black-4 mb-6">
          {getDynamicDescription(item)}
        </p>

        <hr className="my-6" />

        <div className="space-y-2 mb-6">
          {item?.orderItems?.map((order: any, index: number) => {
            return (
              <div className="flex justify-between" key={index}>
                <span className="font-bold text-base tracking-normal align-middle">
                  {order?.Product2?.Name}
                </span>
                <span className="font-normal text-base text-color-grey-15 tracking-normal text-right align-middle">
                  ${order?.UnitPrice}
                </span>
              </div>
            )
          })}
          <div className="flex justify-between">
            {item?.Type === RecordType.FlexiblePayment && (
              <>
                <span className="font-bold text-base tracking-normal align-middle">
                  Split into 3 monthly payments of
                </span>
                <span className="font-normal text-base text-color-grey-15 tracking-normal text-right align-middle">
                  ${((item?.TotalAmount - item?.Discount__c) / 3).toFixed(2)}
                </span>
              </>
            )}
          </div>
        </div>

        <div className="bg-red-100 text-red-800 rounded-lg p-4 mb-6">
          <p className="font-semibold text-red-700">Are you sure?</p>
          <p className="font-normal text-sm text-gray-700 mt-1">
            {(item?.Type === RecordType.MembershipMonthly || item?.Type === RecordType.MembershipAnnual)
              ? 'The student will not be able to purchase the membership.'
              : 'The student will not be able to register to the class.'
            }
          </p>
        </div>

        {/* Actions */}
        <div className="flex justify-end mt-6 gap-6">
          <CustomButton
            title="Reject"
            onClick={() => handleReject(item?.Id)}
            isLoading={isLoading}
            height={10}
            width="222px"
            backgroundColor="bg-color-yellow"
            classes="text-gray-700 border border-gray-200"
            isDisabled={false}
          />
        </div>
      </div>
    </div>
  )
}

export default ClassRejectModal
