import { useAuth } from '@/contexts/AuthContext'
import { profilePhoto } from '@/lib/redux'
import { getNameLetter } from '@/utils/utils'
import { useUser } from '@stackframe/stack'
import Image from 'next/image'
import React from 'react'
import { useSelector } from 'react-redux'

const UserProfileImage = ({
  height,
  width,
  setShowUserBar,
}: {
  height: number
  width: number
  setShowUserBar?: (value: any) => void
}) => {
  const user = useUser()
  const { user: userAuth } = useAuth()
  const profileImage = useSelector(profilePhoto)

  if (!user) {
    return (
      <div
        className={`h-[${height}px] w-[${width}px] bg-color-grey-2 border-2 cursor-pointer rounded-full`}
        onClick={(event) => {
          event.stopPropagation()
          setShowUserBar && setShowUserBar((prev: any) => !prev)
        }}
      ></div>
    )
  }

  return (
    <>
      {(userAuth && userAuth?.photo) || (user && user?.profileImageUrl) ? (
        <Image
          width={width}
          height={height}
          src={
            profileImage != ''
              ? profileImage
              : userAuth?.photo
                ? userAuth?.photo
                : user?.profileImageUrl
          }
          alt="User avatar"
          className="rounded-full object-cover cursor-pointer aspect-square"
          onClick={(event) => {
            event.stopPropagation()
            setShowUserBar && setShowUserBar((prev: any) => !prev)
          }}
        />
      ) : (
        <div
          className="rounded-full bg-black cursor-pointer text-white flex items-center justify-center font-semibold text-base"
          onClick={() => {
            setShowUserBar && setShowUserBar((prev: any) => !prev)
          }}
          style={{ height: `${height}px`, width: `${width}px` }}
        >
          <h3>{getNameLetter(userAuth?.name)}</h3>
        </div>
      )}
    </>
  )
}

export default UserProfileImage
