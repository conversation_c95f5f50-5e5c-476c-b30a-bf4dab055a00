'use client'
import { use<PERSON><PERSON> } from '@/hooks/useApi'
import { useState, useEffect, useRef, useCallback } from 'react'
import React from 'react'
import TransactionCard from '../TransactionCard/TransactionCard'
import {
  getWalletBalance,
  getWalletHistory,
} from '@/lib/actions/wallet.actions'
import { format, lastDayOfYear, startOfYear } from 'date-fns'
import CustomDropDown from '../CustomComponents/CustomDropDown/CustomDropDown'
import dynamic from 'next/dynamic'
import InfiniteScroll from 'react-infinite-scroll-component'
import { PAGINATION_LIMIT } from '@/utils/constants'
import { toast } from 'react-toastify'
import TransactionCardSkeleton from '../Skeletons/TransactionCardSkeleton'
import { getTeacherStats, getTodoList } from '@/lib/actions/teacher.actions'
import { getAllMeetings, getAllMeetingsByIdServer, getClasses, getClassesGroupedByGenre, getEvents, getSubcategoryDropdownValues } from '@/lib/actions/class.actions'
import { followTeachers, getFollowedTeachers } from '@/lib/actions/follow.actions'
import { addParentAccount, getAccountQuotes } from '@/lib/actions/account.actions'
import { getAllParentMeetings, getAllYouthMeetings, getParentTodoList, getYouthClasses, testFunc } from '@/lib/actions/parent.actions'
import { getMembershipProducts } from '@/lib/actions/membership.actions'
import { getActivePaymentToken } from '@/lib/actions/payment.actions'

const AddMoneyToWallet = dynamic(
  () => import('../AddMoneyToWallet/AddMoneyToWallet'),
)

type TransactionType = 'All' | 'Credit' | 'Debit'

const typeOptions = [
  { value: 'All', label: 'All' },
  { value: 'Credit', label: 'Credit' },
  { value: 'Debit', label: 'Debit' },
]

const CreditDetails = () => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [isAddWalletClicked, setIsAddWalletClicked] = useState<boolean>(false)
  const [type, setType] = useState<TransactionType>('All')
  const [transactions, setTransactions] = useState<any[]>([])
  const offset = useRef<number>(0)
  const [hasMore, setHasMore] = useState<boolean>(true)
  const [balance, setBalance] = useState<number>(0)
  const [balanceResponse, fetchBalance] = useApi((access: string) =>
    getWalletBalance(access),
  )

  const [walletHistoryResponse, walletHistory] = useApi(
    (
      access: string,
      type: TransactionType,
      start: any,
      end: any,
      limit: number,
      offset: number,
    ) => getWalletHistory(access, type, start, end, limit, offset),
  )

  const testFunction = async () => {
    console.log("Testing")

    const res = await getClassesGroupedByGenre()
    console.log("Success", res);
  }

  useEffect(() => {
    WalletBalance()
  }, [])

  useEffect(() => {
    if (balanceResponse.isSuccess) {
      setBalance(balanceResponse.data.data.balance)
    }
  }, [balanceResponse])

  useEffect(() => {
    if (walletHistoryResponse.isSuccess) {
      const moreOrders = walletHistoryResponse?.data?.data?.data || []

      if (moreOrders.length > 0) {
        setTransactions((prev: any) => {
          const existingIds = new Set(prev.map((t: any) => t.id))
          const newUniqueOrders = moreOrders.filter(
            (order: any) => !existingIds.has(order.id),
          )

          const newTotal = prev.length + newUniqueOrders.length

          // Check if we have more data to load
          if (
            walletHistoryResponse?.data?.data?.total <= newTotal ||
            moreOrders.length < PAGINATION_LIMIT
          ) {
            setHasMore(false)
          }

          return [...prev, ...newUniqueOrders]
        })
      } else {
        setHasMore(false)
      }
    }

    if (walletHistoryResponse.error) {
      console.error(
        'Error fetching wallet history:',
        walletHistoryResponse.error,
      )
      setHasMore(false)
    }
  }, [walletHistoryResponse])

  useEffect(() => {
    setTransactions([])
    offset.current = 0
    setHasMore(true)
    // Initial fetch when type changes
    fetchWalletHistory()
  }, [type])

  useEffect(() => {
    // Initial fetch on component mount
    fetchWalletHistory()
  }, [])

  const fetchWalletHistory = async () => {
    if (walletHistoryResponse.isFetching) return // Prevent concurrent requests

    const year = currentDate.getFullYear()
    const startDate = format(startOfYear(new Date(year, 0, 1)), 'yyyy-MM-dd')
    const endDate = format(lastDayOfYear(new Date(year, 0, 1)), 'yyyy-MM-dd')

    await walletHistory(
      type,
      startDate,
      endDate,
      PAGINATION_LIMIT,
      offset.current,
    )

    // Update offset after request
    offset.current += PAGINATION_LIMIT
  }

  const WalletBalance = async () => {
    try {
      await fetchBalance()
    } catch (error) {
      toast.error('Failed to fetch wallet balance', {
        autoClose: 1500,
      })
    }
  }

  const handleTypeChange = useCallback(
    (value: any) => setType(value as TransactionType),
    [],
  )

  return (
    <div className="w-full flex justify-center">
      <div className="mt-5 w-full">
        {isAddWalletClicked ? (
          <AddMoneyToWallet
            balance={balance}
            setIsAddWalletClicked={setIsAddWalletClicked}
          />
        ) : (
          <>
            <div className="w-full bg-color-black rounded-2xl flex justify-between p-10 flex-col md:flex-row">
              <div className="flex flex-col gap-2 text-white">
                <h1 className="font-semibold text-4xl">
                  $ {balance.toFixed(2)}
                </h1>
                <h2 className="text-2xl">Account balance</h2>
              </div>
              <button
                type="button"
                onClick={() => testFunction()}
                className="w-56 h-12 bg-color-yellow flex items-center justify-center px-6 py-4 rounded-md text-sm font-bold mt-5 md:mt-0"
              >
                Add Money
              </button>
            </div>

            <div className="my-5 bg-white rounded-2xl px-5 md:px-10 pt-5 pb-10 border">
              <h1 className="font-semibold text-lg">Previous transactions</h1>
              <div className="flex items-center justify-between mt-3">
                <div className="flex items-center gap-3">
                  <CustomDropDown
                    icon={<></>}
                    title={type}
                    options={typeOptions}
                    handleOptionClick={handleTypeChange}
                    width={'w-max'}
                    height={8}
                  />
                  {/* <MonthSelector
                    currentDate={currentDate}
                    setCurrentDate={setCurrentDate}
                  /> */}
                </div>
                {/* <button className="px-3 py-2 bg-color-yellow text-sm font-semibold rounded-lg">
                  Monthly
                </button> */}
              </div>

              {/* Table Headers */}
              <div className="grid grid-cols-4 bg-color-grey-20 gap-4 py-3 px-5 font-medium text-sm mt-5">
                <div>TYPE</div>
                <div>AMOUNT</div>
                <div>DATE</div>
                <div>ORDER</div>
              </div>

              <div
                id="scrollableDiv"
                className="no-scrollbar"
                style={{ height: '600px', overflow: 'auto' }}
              >
                <InfiniteScroll
                  dataLength={transactions.length}
                  next={fetchWalletHistory}
                  hasMore={hasMore}
                  scrollableTarget="scrollableDiv"
                  loader={
                    <div className="w-full flex flex-col mt-10 gap-2">
                      <TransactionCardSkeleton type={type} index={0} />
                      <TransactionCardSkeleton type={type} index={1} />
                      <TransactionCardSkeleton type={type} index={2} />
                      <TransactionCardSkeleton type={type} index={3} />
                    </div>
                  }
                  endMessage={
                    <div className="flex items-center font-bold justify-center mt-10 text-color-grey-1">
                      No more transactions to show
                    </div>
                  }
                  pullDownToRefreshThreshold={50}
                >
                  <div className="flex flex-col">
                    {transactions?.map((transaction, index) => (
                      <div className="w-full" key={transaction.id}>
                        <TransactionCard transaction={transaction} />
                      </div>
                    ))}
                  </div>
                </InfiniteScroll>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default CreditDetails
