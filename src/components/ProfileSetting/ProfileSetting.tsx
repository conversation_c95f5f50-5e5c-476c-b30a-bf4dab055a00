'use client'
import React, { FormEvent, useEffect, useState } from 'react'
import ProfileUpload from '../ProfileUpload/ProfileUpload'
import { updateAccountById } from '@/lib/actions/account.actions'
import { useApi } from '@/hooks/useApi'
import 'react-country-state-city/dist/react-country-state-city.css'
import { toast } from 'react-toastify'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import { useAuth } from '@/contexts/AuthContext'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import AddressCard from '../AddressCard/AddressCard'
import Link from 'next/link'
import { format } from 'date-fns'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css'
import Select from 'react-select'
import { updateUserFields } from '@/lib/actions/login.actions'
interface FormData {
  firstName: string
  lastName: string
  phoneNumber: string
  bio: string
  about: string
  favoriteGenres: any
  favoriteAuthors: <AUTHORS>
  website: string
  youtube: string
  substack: string
  instagram: string
  twitter: string
  facebook: string
  showSocial: boolean
}

const ProfileSetting = ({
  userData,
  genres = [],
  authors = [],
}: {
  userData: any
  genres?: any
  authors?: any
}) => {
  const { user, setUser } = useAuth()
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    bio: '',
    about: '',
    favoriteGenres: [],
    favoriteAuthors: <AUTHORS>
    website: '',
    youtube: '',
    substack: '',
    instagram: '',
    twitter: '',
    facebook: '',
    showSocial: false,
  })

  // store birthday as Date
  const [dateOfBirth, setDateOfBirth] = useState<Date | null>(
    user?.birthday
      ? new Date(user.birthday)
      : userData?.birthday
        ? new Date(userData.birthday)
        : null,
  )

  const [response, updateDetails] = useApi(
    (access: string, data: any) => updateAccountById(access, data),
    true,
  )

  useEffect(() => {
    if (response.isSuccess) {
      toast.success('Profile updated successfully')

      const updateData = {
        name: `${formData.firstName} ${formData.lastName}`.trim(),
        birthday: dateOfBirth ? format(dateOfBirth, 'yyyy-MM-dd') : null, // send clean date
        bio: formData.about,
        about: formData.about,
        favoriteGenres: formData.favoriteGenres?.join(';'), // ⬅️ semi-colon separated
        favoriteAuthors: <AUTHORS>
        website: formData.website,
        youtube: formData.youtube,
        substack: formData.substack,
        instagram: formData.instagram,
        twitter: formData.twitter,
        facebook: formData.facebook,
        phoneNumber: formData.phoneNumber,
      }
      updateUserFields(updateData).then(() => {
        setUser((prev: any) => ({ ...prev, ...updateData })) // update context immediately
      })
    }
  }, [response])

  useEffect(() => {
    if (user?.birthday) {
      setDateOfBirth(new Date(user.birthday))
    }
  }, [user])

  useEffect(() => {
    if (userData) {
      setFormData((prev) => ({
        ...prev,
        firstName: userData.name?.split(' ')[0] || '',
        lastName: userData.name?.split(' ')?.slice(1)?.join(' '),
        phoneNumber: userData.phoneNumber || '',
        bio: userData.bio || '',
        about: userData.bio || '',
        favoriteGenres: userData.favoriteGenres
          ? userData.favoriteGenres?.split(';')
          : [],
        favoriteAuthors: <AUTHORS>
          ? userData.favoriteAuthors?.split(';')
          : [],
        website: userData.website || '',
        youtube: userData.youtube || '',
        substack: userData.substack || '',
        instagram: userData.instagram || '',
        twitter: userData.twitter || '',
        facebook: userData.facebook || '',
      }))
    }
  }, [userData])

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (userData && userData?.id) {
      const updateData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        dateOfBirth: dateOfBirth ? format(dateOfBirth, 'yyyy-MM-dd') : null, // send clean date
        bio: formData.about,
        about: formData.about,
        favoriteGenres: formData.favoriteGenres,
        favoriteAuthors: <AUTHORS>
        website: formData.website,
        youtube: formData.youtube,
        substack: formData.substack,
        instagram: formData.instagram,
        twitter: formData.twitter,
        facebook: formData.facebook,
        phone: formData.phoneNumber,
      }
      updateDetails(updateData)
    }
  }

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handlePhoneChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      phoneNumber: value,
    }))
  }

  const customStyles = {
    control: (base: any, state: any) => ({
      ...base,
      borderColor: state.isFocused ? '#FDBA74' : base.borderColor, // yellow border on focus
      boxShadow: state.isFocused ? '0 0 0 1px #FDBA74' : 'none', // yellow glow
      '&:hover': {
        borderColor: state.isFocused ? '#FDBA74' : base.borderColor,
      },
      padding: '4px 8px',
      borderRadius: '7px',
    }),
  }

  return (
    <div className="w-full">
      {/* <div className="w-full flex items-center justify-center">
        <h1 className="text-base font-bold mb-4 px-3 md:px-0">
          Only{' '}
          <Link
            href={{
              pathname: '/my-account',
              query: { tab: 'Membership' },
            }}
          >
            <span className="text-color-blue-2 underline">
              Muse members
            </span>{' '}
          </Link>
          will have their profiles shown publicly.
        </h1>
      </div> */}
      <div className="w-full border mb-3 bg-color-grey-2"></div>
      <div className="px-5 md:px-10 pb-5 space-y-6">
        <div className="space-y-1">
          <label htmlFor="bio" className="block text-sm font-medium">
            Profile Picture
          </label>
          {userData && (
            <div className="flex items-center gap-5 mb-4">
              <ProfileUpload />
            </div>
          )}
        </div>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* first name */}
          <div className="space-y-1">
            <label htmlFor="firstName" className="block text-sm font-medium">
              First Name
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              placeholder="Enter your first name"
              required
            />
          </div>

          {/* last name */}
          <div className="space-y-1">
            <label htmlFor="lastName" className="block text-sm font-medium">
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              placeholder="Enter your last name"
              required
            />
          </div>

          {/* birthday (custom) */}
          <div className="space-y-1">
            <label className="block text-sm font-medium">Birthday</label>
            <DatePicker
              selected={dateOfBirth}
              onChange={(date: Date | null) => setDateOfBirth(date)}
              dateFormat="MMM dd, yyyy"
              placeholderText="Enter your birthdate"
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              showMonthDropdown
              showYearDropdown
              scrollableYearDropdown
              yearDropdownItemNumber={100}
            />
          </div>
          {/* phone */}
          <div className="space-y-1">
            <label htmlFor="phoneNumber" className="block text-sm font-medium">
              Phone Number
            </label>
            <PhoneInput
              country={'us'}
              value={formData.phoneNumber}
              onChange={handlePhoneChange}
              inputProps={{
                name: 'parentPhoneNumber',
                required: true,
                className: 'w-full p-2 border border-gray-300 rounded pl-12',
              }}
            />
          </div>

          {/* about */}
          {/* <div className="space-y-1">
            <label htmlFor="about" className="block text-sm font-medium">
              About
            </label>
            <textarea
              id="about"
              name="about"
              value={formData.about}
              onChange={handleChange}
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              rows={4}
            />
          </div> */}
          {userData && <AddressCard userData={userData} />}

          {/* genre */}

          {/* <div className="space-y-1">
            <label htmlFor="genre" className="block text-sm font-medium">
              Genres
            </label>
            <Select
              isMulti
              name="genre"
              options={genres}
              value={genres?.filter((opt: any) =>
                formData?.favoriteGenres?.includes(opt.value),
              )}
              onChange={(selected: any) =>
                setFormData((prev) => ({
                  ...prev,
                  favoriteGenres: selected?.map((s: any) => s.value), // store as string[]
                }))
              }
              className="basic-multi-select"
              classNamePrefix="select genre"
              styles={customStyles}
            />
          </div> */}

          {/* <div className="space-y-1">
            <label htmlFor="authors" className="block text-sm font-medium">
              Authors
            </label>
            <Select
              isMulti
              name="authors"
              options={authors}
              value={authors?.filter((opt: any) =>
                formData?.favoriteAuthors?.includes(opt.value),
              )}
              onChange={(selected: any) =>
                setFormData((prev) => ({
                  ...prev,
                  favoriteAuthors: <AUTHORS>
                }))
              }
              className="basic-multi-select"
              classNamePrefix="select author"
              styles={customStyles}
            />
          </div> */}

          {/* website + socials */}
          {/* <div className="space-y-1">
            <label htmlFor="website" className="block text-sm font-medium">
              Website
            </label>
            <input
              type="url"
              id="website"
              name="website"
              value={formData.website}
              onChange={handleChange}
              className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
              placeholder="https://yourwebsite.com"
            />
          </div> */}

          {/* socials */}
          {/* <div className="space-y-4">
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="instagram"
                  className="block text-sm font-medium mb-1"
                >
                  Instagram
                </label>
                <input
                  type="url"
                  id="instagram"
                  name="instagram"
                  value={formData.instagram}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="https://www.instagram.com/username"
                />
              </div>

              <div>
                <label
                  htmlFor="twitter"
                  className="block text-sm font-medium mb-1"
                >
                  X (Previously Twitter)
                </label>
                <input
                  type="url"
                  id="twitter"
                  name="twitter"
                  value={formData.twitter}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="https://x.com/username"
                />
              </div>

              <div>
                <label
                  htmlFor="facebook"
                  className="block text-sm font-medium mb-1"
                >
                  Facebook
                </label>
                <input
                  type="url"
                  id="facebook"
                  name="facebook"
                  value={formData.facebook}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="https://www.facebook.com/username"
                />
              </div>

              <div>
                <label
                  htmlFor="youtube"
                  className="block text-sm font-medium mb-1"
                >
                  YouTube
                </label>
                <input
                  type="url"
                  id="youtube"
                  name="youtube"
                  value={formData.youtube}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="https://www.youtube.com/username"
                />
              </div>

              <div>
                <label
                  htmlFor="substack"
                  className="block text-sm font-medium mb-1"
                >
                  Substack
                </label>
                <input
                  type="url"
                  id="substack"
                  name="substack"
                  value={formData.substack}
                  onChange={handleChange}
                  className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                  placeholder="https://www.substack.com/username"
                />
              </div>
            </div>
          </div> */}

          <CustomButton
            title="Update"
            isLoading={response.isFetching}
            onClick={handleSubmit}
          />
        </form>
      </div>
    </div>
  )
}

export default ProfileSetting
