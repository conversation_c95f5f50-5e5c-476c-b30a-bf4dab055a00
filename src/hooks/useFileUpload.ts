import { useState } from 'react'
import { toast } from 'react-toastify'
import { getPresignedPutUrl, getPublicUrl } from '@/lib/aws'
import imageCompression from 'browser-image-compression'
import { v4 as uuidv4 } from 'uuid'

interface UseFileUploadOptions {
  onSuccess?: (fileUrl: string) => void
  onError?: (error: any) => void
  filePrefix?: string
  maxImageSize?: number // in bytes, default 5MB
  maxPdfSize?: number // in bytes, default 10MB
  maxOtherSize?: number // in bytes, default 10MB
  enableImageCompression?: boolean // default true
  compressionOptions?: {
    maxSizeMB?: number // default 1MB after compression
    maxWidthOrHeight?: number // default 1920px
    useWebWorker?: boolean // default true
    initialQuality?: number // default 0.8
  }
}

interface UseFileUploadReturn {
  isUploading: boolean
  uploadedFileUrl: string | null
  handleFileUpload: (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => Promise<string | null>
  resetUpload: () => void
  validateFileSize: (file: File) => boolean
}

export const useFileUpload = (
  options: UseFileUploadOptions = {},
): UseFileUploadReturn => {
  const {
    onSuccess,
    onError,
    filePrefix = '',
    maxImageSize = 5 * 1024 * 1024, // 5MB
    maxPdfSize = 10 * 1024 * 1024, // 10MB
    maxOtherSize = 10 * 1024 * 1024, // 10MB
    enableImageCompression = true,
    compressionOptions = {
      maxSizeMB: 1, // Compress to max 1MB
      maxWidthOrHeight: 1920, // Max dimension 1920px
      useWebWorker: true, // Use web worker for non-blocking compression
      initialQuality: 0.8, // Start with 80% quality
    },
  } = options

  const [isUploading, setIsUploading] = useState(false)
  const [uploadedFileUrl, setUploadedFileUrl] = useState<string | null>(null)

  const compressImageIfNeeded = async (file: File): Promise<File> => {
    const isImage = file.type.startsWith('image/')

    if (!isImage || !enableImageCompression) {
      return file
    }

    try {
      // Show compression progress
      const compressedFile = await imageCompression(file, {
        ...compressionOptions,
        onProgress: (progress) => {
          // You can add a progress callback here if needed
        },
      })

      // Log compression results
      const originalSizeMB = (file.size / (1024 * 1024)).toFixed(2)
      const compressedSizeMB = (compressedFile.size / (1024 * 1024)).toFixed(2)
      return compressedFile
    } catch (error) {
      console.warn('Image compression failed, using original file:', error)
      return file
    }
  }

  const validateFileSize = (file: File): boolean => {
    const isImage = file.type.startsWith('image/')
    const isPDF = file.type === 'application/pdf'

    let maxSize: number
    let fileTypeText: string

    if (isImage) {
      maxSize = maxImageSize
      fileTypeText = `${Math.round(maxImageSize / (1024 * 1024))}MB`
    } else if (isPDF) {
      maxSize = maxPdfSize
      fileTypeText = `${Math.round(maxPdfSize / (1024 * 1024))}MB`
    } else {
      maxSize = maxOtherSize
      fileTypeText = `${Math.round(maxOtherSize / (1024 * 1024))}MB`
    }

    if (file.size > maxSize) {
      toast.error(
        `File size must be less than ${fileTypeText}. Please choose a smaller file.`,
        {
          autoClose: 3000,
        },
      )
      return false
    }

    return true
  }

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ): Promise<string | null> => {
    const acceptTypes = event.target.accept
    const acceptArray = acceptTypes.split(',').map((type) => type.trim())
    const file = event.target.files?.[0]
    if (!file) return null
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
    const isAcceptedType = acceptArray.includes(fileExtension)
    if (!isAcceptedType && acceptArray[0] != '') {
      toast.error(
        `Please select a valid file type. Accepted formats: ${acceptTypes}`,
        {
          autoClose: 3000,
        },
      )
      event.target.value = ''
      return null
    }
    // Validate file size BEFORE compression
    if (!validateFileSize(file)) {
      event.target.value = ''
      return null
    }

    setIsUploading(true)

    try {
      // Compress image if needed
      const processedFile = await compressImageIfNeeded(file)

      const timeStamp = uuidv4()
      const fileName = `${filePrefix}_${timeStamp}_${processedFile.name}`

      // Get presigned URL and upload file
      const presignedUrl = await getPresignedPutUrl(
        fileName,
        processedFile.type,
      )
      const uploadResponse = await fetch(presignedUrl, {
        method: 'PUT',
        body: processedFile,
        headers: {
          'Content-Type': processedFile.type,
        },
      })

      if (uploadResponse.ok) {
        // Get the public URL of the uploaded file
        const fileUrl = await getPublicUrl(fileName)
        setUploadedFileUrl(fileUrl)

        const isCompressed = processedFile.size < file.size
        const message = isCompressed
          ? `File compressed and uploaded successfully!`
          : 'File uploaded successfully!'

        toast.success(message, { autoClose: 1500 })

        if (onSuccess) {
          onSuccess(fileUrl)
        }

        return fileUrl
      } else {
        event.target.value = ''
        throw new Error('Upload failed')
      }
    } catch (error) {
      console.error('File upload error:', error)
      toast.error('File upload failed. Please try again.', { autoClose: 3000 })

      if (onError) {
        event.target.value = ''
        onError(error)
      }

      return null
    } finally {
      setIsUploading(false)
    }
  }

  const resetUpload = () => {
    setUploadedFileUrl(null)
    setIsUploading(false)
  }

  return {
    isUploading,
    uploadedFileUrl,
    handleFileUpload,
    resetUpload,
    validateFileSize,
  }
}
