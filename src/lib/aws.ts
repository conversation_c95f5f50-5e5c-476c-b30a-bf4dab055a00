'use server'
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

const client = new S3Client({
  region: 'us-east-1',
  credentials:
    process.env.ENV === 'local'
      ? {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        }
      : undefined,
})

export const getPresignedPutUrl = async (
  fileName: string,
  contentType: string,
) => {
  const command = new PutObjectCommand({
    Bucket: process.env.S3_BUCKET_NAME,
    Key: fileName,
    ContentType: contentType,
  })

  const url = await getSignedUrl(client, command, { expiresIn: 60 })

  return url
}

export const getPublicUrl = async (fileName: string) => {
  const url = `https://${process.env.S3_BUCKET_NAME}.s3.us-east-1.amazonaws.com/${fileName}`
  return url
}
