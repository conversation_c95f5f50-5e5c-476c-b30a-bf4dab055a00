'use server'

import jsforce from 'jsforce'

import {
  getAccountAwardedBadgesServer,
  getAccountId,
  getOrCreateAccountServer,
  getParentAccountIdServer,
} from '@/lib/actions/account.actions'
import {
  getClassesBadgesByTypeServer,
  getClassMinimumPriceByIdServer,
} from '@/lib/actions/class.actions'
import {
  applyCouponIdsToCartBulkServer,
  applyCouponToCartServer,
  getCouponCodesForUserServer,
  getCouponDataServer,
  getCouponIdFromCodeServer,
  getCouponMetaDataBulkServer,
} from '@/lib/actions/coupon.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import { getDonationProduct } from '@/lib/actions/salesforce.actions'
import { getWalletBalanceServer } from '@/lib/actions/wallet.actions'
import { createPaymentToken, DecodedPaymentToken } from '@/lib/jwt'
import { connectToSalesforce } from '@/lib/salesforce'
import { IPaymentToken, Schedule } from '@/models/paymentToken.model'
import { PaymentType } from '@/utils/constants'
import { responseGenerator } from '@/utils/utils'
import { connectToMongoDB } from '@/lib/db'
import {
  getPriceBookIdByProductServer,
  createPaymentTokenSF,
} from '@/lib/actions/payment.actions'

interface FlexiblePrice {
  occurrences: number
  schedule: Schedule
  splitPrice: number
}

export const calculateFlexiblePriceServer = async (
  amount: number,
): Promise<FlexiblePrice> => {
  if (!Number.isFinite(amount) || amount < 0) {
    throw new Error('Amount must be a positive number.')
  }

  const OCCURRENCES_FOR_MONTHLY = 3
  const roundToCents = (n: number) => Math.round(n * 100) / 100

  const raw = amount / OCCURRENCES_FOR_MONTHLY
  return {
    occurrences: OCCURRENCES_FOR_MONTHLY,
    schedule: 'Monthly',
    splitPrice: roundToCents(raw),
  }
}

//soql to get product of a class - Select Id,Name,Price__c,Family from Product2 where Class__c ='a0GDw00000LInYfMAL'
export const addToCart = async (
  accessToken: string,
  classId: string,
  userAgreesToPay?: number,
  attendType?: 'In Person' | 'Online',
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    const cartAddQuery = `SELECT Id from Cart_Item__c where Account__c  = '${accessTokenUserId}' AND Product__r.Class__c  = '${classId}'`

    const result = (await conn.query(cartAddQuery)).records
    const isClassAddedToCart = result.length > 0
    if (isClassAddedToCart) {
      return responseGenerator(false, {
        errors: ['Class already added to cart'],
      })
    }

    const userBadges = await getAccountAwardedBadgesServer(accessTokenUserId)
    const classPrerequisiteBadges = await getClassesBadgesByTypeServer(
      classId,
      'Prerequisite',
    )

    const userBadgeIds = new Set(userBadges.map((badge) => badge.id))

    const hasAllPrerequisites = classPrerequisiteBadges.every((prereq) =>
      userBadgeIds.has(prereq.id),
    )

    if (!hasAllPrerequisites) {
      return responseGenerator(false, {
        errors: ['user does not have prerequisite badges'],
      })
    }

    const { is_flexible_price, minimum_price } =
      await getClassMinimumPriceByIdServer(classId)

    if (is_flexible_price && userAgreesToPay) {
      if (userAgreesToPay < minimum_price) {
        throw new Error('price less than minimum_price')
      }
    }
    let soql = `Select Id,Name,Price__c,Family,Class__r.Type__c from Product2 where Class__c ='${classId}'`
    const product = (await conn.query(soql)).records[0]

    //create a new cart item in a user's cart
    const newCartItem: any = {
      Product__c: product.Id,
      Account__c: accessTokenUserId,
    }

    if (is_flexible_price && userAgreesToPay) {
      newCartItem.Unit_Price__c = userAgreesToPay
    }

    if (product.Class__r.Type__c === 'Hybrid') {
      newCartItem.Attendance_Type__c = attendType
    }

    await conn.sobject('Cart_Item__c').create(newCartItem)
    return responseGenerator(true, null)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: [err],
    })
  }
}

export const removeFromCart = async (
  accessToken: string,
  cartItemId: string,
) => {
  if (!(await verifyCustomAccountSession(accessToken))) {
    return responseGenerator(false, { errors: ['Unauthorized'] }, true)
  }
  const conn = await connectToSalesforce()
  await conn.sobject('Cart_Item__c').delete(cartItemId)
  return responseGenerator(true, null)
}

export const getCart = async (accessToken: string, userId: string) => {
  if (!(await verifyCustomAccountSession(accessToken))) {
    return responseGenerator(false, { errors: ['Unauthorized'] }, true)
  }

  const accessTokenUserId = await getAccountId(accessToken)
  if (!accessTokenUserId) {
    return responseGenerator(false, { errors: ['Unauthorized'] }, true)
  }

  const userCouponIds = await getCouponCodesForUserServer(accessTokenUserId)

  const couponMetaDataList = await getCouponMetaDataBulkServer(userCouponIds)

  const couponIdTotalDiscountMap = await applyCouponIdsToCartBulkServer(
    userCouponIds,
    accessTokenUserId,
  )

  const conn = await connectToSalesforce()
  let soql = `SELECT Id,Product__r.Name,Unit_Price__c ,Product__r.Image__c,Product__r.Family,Product__r.Class__c FROM Cart_Item__c WHERE Account__c ='${accessTokenUserId}' ORDER BY CreatedDate desc`
  const cartItems = (await conn.query(soql)).records

  const couponDataList = couponMetaDataList.map((item) => {
    return {
      id: item.id,
      name: item.name,
      description: item.description,
      totalDiscount: couponIdTotalDiscountMap.get(item.id!),
    }
  })

  const returnData = {
    cartItems,
    couponCodes: couponDataList,
  }
  return responseGenerator(true, returnData)
}

export const getCartCoupons = async (accessToken: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)
    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const userCouponIds = await getCouponCodesForUserServer(accessTokenUserId)

    const couponMetaDataList = await getCouponMetaDataBulkServer(userCouponIds)

    const couponIdTotalDiscountMap = await applyCouponIdsToCartBulkServer(
      userCouponIds,
      accessTokenUserId,
    )

    const couponDataList = couponMetaDataList.map((item) => {
      return {
        id: item.id,
        name: item.name,
        description: item.description,
        totalDiscount: couponIdTotalDiscountMap.get(item.id!),
      }
    })

    return responseGenerator(true, couponDataList)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, { errors: ['error getting cart coupons'] })
  }
}

export const checkoutCart = async (
  accessToken: string,
  isFlexiblePayment: boolean,
  couponCode?: string,
  couponType?: 'User' | 'General',
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    let priceDiff = undefined
    const conn = await connectToSalesforce()

    let couponId = undefined

    if (couponCode && couponType) {
      couponId = couponCode
      if (couponType === 'General') {
        couponId = await getCouponIdFromCodeServer(couponCode)
      }
      const couponData = await getCouponDataServer(couponId)
      const totalDiscountFromCoupon = await applyCouponToCartServer(
        accessTokenUserId,
        couponData.discount_type,
        couponData.discount_amount,
        couponData.semester,
        couponData.year,
        couponData.class_type,
      )
      priceDiff = totalDiscountFromCoupon
    }

    let soql = `Select SUM(Unit_Price__c) price from Cart_Item__c group by Account__c having Account__c ='${accessTokenUserId}'`
    const response = (await conn.query(soql)).records[0]
    const totalCheckoutPrice = response ? response.price : 0
    const userWalletBalance = await getWalletBalanceServer(accessTokenUserId)
    const paymentType: PaymentType = 'One-time' //TODO: hardcoding it for now
    const finalAmount =
      Math.round((totalCheckoutPrice - (priceDiff ?? 0)) * 100) / 100

    const paymentTokenData: DecodedPaymentToken = {
      userId: accessTokenUserId,
      entityId: accessTokenUserId,
      entityType: 'Cart',
      amount: finalAmount,
      walletBalance: userWalletBalance,
      appliedCoupon: couponId,
      discountedAmount: priceDiff,
      paymentType,
      isWalletEnabled: true,
    }

    const { splitPrice, schedule, occurrences } =
      await calculateFlexiblePriceServer(finalAmount)
    if (isFlexiblePayment) {
      paymentTokenData.amount = splitPrice
      paymentTokenData.paymentType = 'Subscription'
      paymentTokenData.isWalletEnabled = false
    }

    const paymentJwtToken = createPaymentToken(paymentTokenData)

    //create a new payment token for the user
    const paymentToken: IPaymentToken = {
      userId: accessTokenUserId,
      token: paymentJwtToken,
      status: 'active',
      amount: finalAmount,
      paymentType: 'One-time',
      occurrences: paymentType === 'One-time' ? 1 : 9999, //9999 for ongoing subscription
    }

    if (isFlexiblePayment) {
      paymentToken.amount = splitPrice
      paymentToken.paymentType = 'Subscription'
      paymentToken.occurrences = occurrences
      paymentToken.schedule = schedule
    }

    await createPaymentTokenSF(paymentToken)

    const returnData = {
      token: paymentJwtToken,
    }

    return responseGenerator(true, returnData)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const youthCheckoutCart = async (
  accessToken: string,
  isFlexiblePayment: boolean,
  couponCode?: string,
  couponType?: 'User' | 'General',
) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const parentAccountId = await getParentAccountIdServer(accessTokenUserId)

    let priceDiff = undefined
    const conn = await connectToSalesforce()

    let couponId = undefined

    if (couponCode && couponType) {
      couponId = couponCode
      if (couponType === 'General') {
        couponId = await getCouponIdFromCodeServer(couponCode)
      }
      const couponData = await getCouponDataServer(couponId)
      const totalDiscountFromCoupon = await applyCouponToCartServer(
        accessTokenUserId,
        couponData.discount_type,
        couponData.discount_amount,
        couponData.semester,
        couponData.year,
        couponData.class_type,
      )
      priceDiff = totalDiscountFromCoupon
    }

    let soql = `Select SUM(Unit_Price__c) price from Cart_Item__c group by Account__c having Account__c ='${accessTokenUserId}'`
    const response = (await conn.query(soql)).records[0]
    const totalCheckoutPrice = response ? response.price : 0
    const userWalletBalance = await getWalletBalanceServer(accessTokenUserId)
    const paymentType: PaymentType = 'One-time' //TODO: hardcoding it for now
    const finalAmount =
      Math.round((totalCheckoutPrice - (priceDiff ?? 0)) * 100) / 100

    //create order object in salesforce
    const cartOrder = await conn.sobject('Order').create({
      Account__c: accessTokenUserId,
      AccountId: accessTokenUserId,
      Status: 'Draft',
      EffectiveDate: jsforce.SfDate.toDateLiteral(new Date()),
      Discount__c: priceDiff,
      Parent_Approval_Status__c: 'Pending',
      Coupon_Applied__c: !!(couponCode && priceDiff),
    })

    const orderId = cartOrder.id
    if (!orderId) {
      throw new Error('Failed to create order')
    }

    //for every product in the cartItem, create an orderItem object in salesforce
    const cart_soql = `Select Id,Product__r.Id, Unit_Price__c, Attendance_Type__c from Cart_Item__c where Account__c ='${accessTokenUserId}'`
    const cartItems = (await conn.query(cart_soql)).records

    // Calculate total cart amount for proportional discount
    const totalCartAmount = cartItems.reduce(
      (sum, item) => sum + (item.Unit_Price__c ?? 0),
      0,
    )

    // Calculate flexible price for subscription term
    const { splitPrice, schedule, occurrences } =
      await calculateFlexiblePriceServer(finalAmount)

    for (const cartItem of cartItems) {
      const productId = cartItem.Product__r.Id!
      const originalPrice = cartItem.Unit_Price__c
      const pricebookId = await getPriceBookIdByProductServer(productId)

      // Calculate proportional discount for this item
      const itemWeightage = originalPrice / totalCartAmount
      const itemDiscount = priceDiff ? priceDiff * itemWeightage : 0
      const discountedPrice = originalPrice - itemDiscount
      const orderItem = {
        Product2Id: cartItem.Product__r.Id,
        Account__c: accessTokenUserId,
        Quantity: 1,
        OrderId: orderId,
        UnitPrice: discountedPrice, // Price after proportional discount
        Attendance_Type__c: cartItem.Attendance_Type__c,
        PricebookEntryId: pricebookId,
        Subscription_Term__c: isFlexiblePayment ? schedule : undefined,
      }
      await conn.sobject('OrderItem').create(orderItem)
    }

    const paymentTokenData: DecodedPaymentToken = {
      userId: accessTokenUserId,
      entityId: orderId,
      entityType: 'Youth Cart',
      amount: finalAmount,
      walletBalance: userWalletBalance,
      parentAccountId: parentAccountId,
      paymentType,
      isWalletEnabled: true,
    }

    if (isFlexiblePayment) {
      paymentTokenData.amount = splitPrice
      paymentTokenData.paymentType = 'Subscription'
      paymentTokenData.isWalletEnabled = false
    }

    const paymentJwtToken = createPaymentToken(paymentTokenData)

    //create a new payment token for the user
    const paymentToken: IPaymentToken = {
      userId: accessTokenUserId,
      token: paymentJwtToken,
      status: 'active',
      amount: finalAmount,
      paymentType: 'One-time',
      occurrences: paymentType === 'One-time' ? 1 : 9999, //9999 for ongoing subscription
    }

    if (isFlexiblePayment) {
      paymentToken.amount = splitPrice
      paymentToken.paymentType = 'Subscription'
      paymentToken.occurrences = occurrences
      paymentToken.schedule = schedule
    }

    await createPaymentTokenSF(paymentToken)

    //update order object with the JWT token
    await conn.sobject('Order').update({
      Id: orderId,
      Checkout_JWT_Token__c: paymentJwtToken,
    })

    //empty the cart
    for (const cartItem of cartItems) {
      await conn.sobject('Cart_Item__c').delete(cartItem.Id as string)
    }

    return responseGenerator(true, null)
  } catch (error) {
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}

export const makeDonation = async (
  accessToken: string,
  amount: number,
  donationType: string,
  isRecurringDonation: boolean,
  schedule?: 'Monthly' | 'Annual',
) => {
  try {
    // Validate input parameters
    if (!amount || amount <= 0) {
      return responseGenerator(false, {
        errors: ['Amount must be greater than 0'],
      })
    }

    if (isRecurringDonation && !schedule) {
      return responseGenerator(false, {
        errors: ['Schedule is required for recurring donations'],
      })
    }

    if (
      isRecurringDonation &&
      schedule &&
      !['Monthly', 'Annual'].includes(schedule)
    ) {
      return responseGenerator(false, {
        errors: ['Schedule must be either "Monthly" or "Annual"'],
      })
    }

    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const userWalletBalance = await getWalletBalanceServer(accessTokenUserId)

    const product = await getDonationProduct(donationType)

    const paymentTokenData: DecodedPaymentToken = {
      userId: accessTokenUserId,
      entityId: product.Id as string,
      entityType: 'Donation',
      amount,
      walletBalance: userWalletBalance,
      paymentType: 'One-time',
      isWalletEnabled: false, //wallet disabled for donations
    }

    let occurrences = 1
    let paymentSchedule: Schedule | undefined = undefined

    if (isRecurringDonation && schedule) {
      occurrences = 9999 // Infinite occurrences for recurring donations
      paymentSchedule = schedule
      paymentTokenData.paymentType = 'Subscription'
    }

    const paymentJwtToken = createPaymentToken(paymentTokenData)

    //create a new payment token for the user
    const paymentToken: IPaymentToken = {
      userId: accessTokenUserId,
      token: paymentJwtToken,
      status: 'active',
      amount: amount, // Always use the full amount
      paymentType: isRecurringDonation ? 'Subscription' : 'One-time',
      occurrences: occurrences,
    }

    if (isRecurringDonation && paymentSchedule) {
      paymentToken.schedule = paymentSchedule
    }

    await createPaymentTokenSF(paymentToken)

    const returnData = {
      token: paymentJwtToken,
    }

    return responseGenerator(true, returnData)
  } catch (error) {
    console.log('Error: ', error)
    return responseGenerator(false, {
      errors: ['error creating donation'],
    })
  }
}

export const makeAnonymousDonation = async (
  firstName: string,
  lastName: string,
  email: string,
  amount: number,
  donationType: string,
  isRecurringDonation: boolean,
  isDonationAnonymous: boolean,
  schedule?: 'Monthly' | 'Annual',
) => {
  try {
    // Validate input parameters
    if (!firstName || !lastName || !email) {
      return responseGenerator(false, {
        errors: ['First name, last name, and email are required'],
      })
    }

    if (!amount || amount <= 0) {
      return responseGenerator(false, {
        errors: ['Amount must be greater than 0'],
      })
    }

    if (isRecurringDonation && !schedule) {
      return responseGenerator(false, {
        errors: ['Schedule is required for recurring donations'],
      })
    }

    if (
      isRecurringDonation &&
      schedule &&
      !['Monthly', 'Annual'].includes(schedule)
    ) {
      return responseGenerator(false, {
        errors: ['Schedule must be either "Monthly" or "Annual"'],
      })
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return responseGenerator(false, {
        errors: ['Please provide a valid email address'],
      })
    }

    const { account } = await getOrCreateAccountServer(
      {
        FirstName: firstName,
        LastName: lastName,
        PersonEmail: email,
      },
      undefined,
      email,
    )

    const userId = account?.Id! as string

    const userWalletBalance = await getWalletBalanceServer(userId)

    const product = await getDonationProduct(donationType)

    const paymentTokenData: DecodedPaymentToken = {
      userId: userId,
      entityId: product.Id as string,
      entityType: 'Donation',
      amount,
      walletBalance: userWalletBalance,
      isDonationAnonymous: isDonationAnonymous,
      paymentType: 'One-time',
      isWalletEnabled: false, //wallet disabled for donations
    }

    let occurrences = 1
    let paymentSchedule: Schedule | undefined = undefined

    if (isRecurringDonation && schedule) {
      occurrences = 9999 // Infinite occurrences for recurring donations
      paymentSchedule = schedule
      paymentTokenData.paymentType = 'Subscription'
    }

    const paymentJwtToken = createPaymentToken(paymentTokenData)

    //create a new payment token for the user
    const paymentToken: IPaymentToken = {
      userId: userId,
      token: paymentJwtToken,
      status: 'active',
      amount: amount, // Always use the full amount
      paymentType: isRecurringDonation ? 'Subscription' : 'One-time',
      occurrences: occurrences,
    }

    if (isRecurringDonation && paymentSchedule) {
      paymentToken.schedule = paymentSchedule
    }

    await createPaymentTokenSF(paymentToken)

    const returnData = {
      token: paymentJwtToken,
    }

    return responseGenerator(true, returnData)
  } catch (err) {
    console.log('Error: ', err)
    return responseGenerator(false, {
      errors: ['error creating donation'],
    })
  }
}
