'use server'

import jsforce from 'jsforce'
import { connectToSalesforce } from '@/lib/salesforce'
import { arrayToSqlInQuery, responseGenerator } from '@/utils/utils'

import { getAccountId } from '@/lib/actions/account.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import { startOfDay, endOfDay } from '@/utils/utils'
import { getBenefitsForMembershipProductServer } from './membership.actions'

export const getOrders = async (
  accessToken: string,
  startDate: string,
  endDate: string,
  limit = 10,
  offset = 0,
) => {
  const from = startOfDay(startDate)
  const to = endOfDay(endDate)
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const conn = await connectToSalesforce()
    let soql = `Select Id,OrderNumber,CreatedDate,BillingAddress,Discount__c from Order where Account__c = '${accessTokenUserId}' AND CreatedDate >= ${jsforce.SfDate.toDateTimeLiteral(new Date(from))} AND CreatedDate <= ${jsforce.SfDate.toDateTimeLiteral(new Date(to))} ORDER BY CreatedDate DESC NULLS LAST LIMIT ${limit} OFFSET ${offset}`
    let countSoql = `Select Id from Order where Account__c = '${accessTokenUserId}' AND CreatedDate >= ${jsforce.SfDate.toDateTimeLiteral(new Date(from))} AND CreatedDate <= ${jsforce.SfDate.toDateTimeLiteral(new Date(to))}`

    const orders = (await conn.query(soql)).records
    const countResponse = (await conn.query(countSoql)).totalSize

    const ordersWithItemsAndTotalPrice = []

    for (let order of orders) {
      const orderItems = (
        await conn.query(
          `SELECT Id,Product2.Family,Product2.Class__c,Product2.Name,Product2.Image__c,TotalPrice,Quantity,UnitPrice FROM OrderItem WHERE OrderId = '${order.Id}'`,
        )
      ).records
      const totalPrice = orderItems.reduce(
        (acc: number, item: any) => acc + item.TotalPrice,
        0,
      )

      const payments = (
        await conn.query(
          `SELECT Id,Amount__c, Account_Type__c, Account_Number__c FROM Payment__c WHERE Order__c = '${order.Id}'`,
        )
      ).records

      ordersWithItemsAndTotalPrice.push({
        order,
        orderItems,
        totalPrice,
        payments,
      })
    }

    const data = {
      data: ordersWithItemsAndTotalPrice,
      total: countResponse,
    }

    return responseGenerator(true, data)
  } catch (error) {
    return responseGenerator(false, { errors: ['Failed to get orders'] }, true)
  }
}

export const getApprovalPendingOrderIdsServer = async (youthIds: string[]) => {
  if (youthIds.length === 0) return []
  const conn = await connectToSalesforce()
  const soql = `SELECT Id FROM Order WHERE Parent_Approval_Status__c = 'Pending' AND Account__c IN ${arrayToSqlInQuery(youthIds)}`

  const records = (await conn.query(soql)).records
  const orderIds = records.map((item) => item.Id!)

  return orderIds
}

export const getOrderDetailForParentServer = async (orderId: string) => {
  const conn = await connectToSalesforce()
  const order_soql = `SELECT Id, Account__r.Name,Account__r.Age__c, Account__r.PersonBirthdate, CreatedDate, TotalAmount, Discount__c, Checkout_JWT_Token__c, Type FROM Order WHERE Id = '${orderId}'`
  const records = (await conn.query(order_soql)).records
  const order = records[0]
  if (!order) {
    throw new Error('Order not found')
  }

  const order_items_soql = `SELECT Id, Product2.Name, UnitPrice, Product2Id  FROM OrderItem WHERE OrderId = '${orderId}'`
  const records2 = (await conn.query(order_items_soql)).records

  const benefits = records2.length > 0 ? await getBenefitsForMembershipProductServer(
    records2[0].Product2Id,
  ): []

  const returnData = {
    ...order,
    orderItems: records2,
    benefits,
  }

  return returnData
}

export const getAllOrderProductsByOrderIdServer = async (orderId: string) => {
  const conn = await connectToSalesforce()
  const order_items_soql = `SELECT Id, Product2.Name, Product2.Class__c, Product2.Class__r.Flexible_Pricing__c, UnitPrice, ListPrice, Attendance_Type__c  FROM OrderItem WHERE OrderId = '${orderId}'`
  const records = (await conn.query(order_items_soql)).records
  return records
}
