'use client'

import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { useSearchParams } from 'next/navigation'

import Edit<PERSON>erson from '@/components/Icons/EditPerson'
import BellIcon from '@/components/Icons/BellIcon'
import PaymentMethod from '@/components/Icons/PaymentMethod'
import Wallet from '@/components/Icons/Wallet'
import Help from '@/components/Icons/Help'
import LogoutButton from '@/components/LogoutButton/LogoutButton'
import SettingIcon from '@/components/Icons/SettingIcon'
import Membership from '@/components/Icons/Membership'
import MembershipPlans from '@/components/MembershipPlans/MembershipPlans'
import { ROLES } from '@/types/enum'
import CustomDropDown from '@/components/CustomComponents/CustomDropDown/CustomDropDown'
import Sort from '@/components/Icons/Sort'
import { useAuth } from '@/contexts/AuthContext'
import Order from '@/components/Icons/Order'
import TransactionIcon from '@/components/Icons/TransactionIcon'

const ProfileSetting = dynamic(
  () => import('@/components/ProfileSetting/ProfileSetting'),
  { ssr: false },
)
const NotificationsSetting = dynamic(
  () => import('@/components/NotificationsSetting/NotificationsSetting'),
  { ssr: false },
)
const PaymentSettings = dynamic(
  () => import('@/components/PaymentSettings/PaymentSettings'),
  { ssr: false },
)
const AccountSettings = dynamic(
  () => import('@/components/AccountSettings/AccountSettings'),
  { ssr: false },
)
const HelpdeskSetting = dynamic(
  () => import('@/components/HelpdeskSetting/HelpdeskSetting'),
  { ssr: false },
)
const CreditDetails = dynamic(
  () => import('@/components/CreditDetails/CreditDetails'),
  { ssr: false },
)

const OrderListing = dynamic(
  () => import('@/components/OrdersListing/OrdersListing'),
  { ssr: false },
)

const accountLinks = [
  {
    title: 'Edit Profile',
    component: 'ProfileSetting',
    icon: (isActive: boolean) => <EditPerson isActive={isActive} />,
  },
  {
    title: 'Membership',
    component: 'Membership',
    icon: (isActive: boolean) => <Membership isActive={isActive} />,
  },
  {
    title: 'Wallet',
    component: 'AddMoneyToWallet',
    icon: (isActive: boolean) => <Wallet isActive={isActive} />,
  },
  {
    title: 'Transactions',
    component: 'MyTransactions',
    icon: (isActive: boolean) => <TransactionIcon isActive={isActive} />,
  },
  {
    title: 'Notification Settings',
    component: 'NotificationsSetting',
    icon: (isActive: boolean) => <BellIcon isActive={isActive} />,
  },
  {
    title: 'Payment Methods',
    component: 'PaymentSettings',
    icon: (isActive: boolean) => <PaymentMethod isActive={isActive} />,
  },
  {
    title: 'Account Settings',
    component: 'AccountSettings',
    icon: (isActive: boolean) => <SettingIcon isActive={isActive} />,
  },
  {
    title: 'Helpdesk & FAQs',
    component: 'HelpdeskSetting',
    icon: (isActive: boolean) => <Help isActive={isActive} />,
  },
]

function getTabComponent(tab: string, user: any, genres: any, authors: any) {
  switch (tab) {
    case 'ProfileSetting':
      return (
        <ProfileSetting userData={user} genres={genres} authors={authors} />
      )
    case 'Membership':
      return <MembershipPlans />
    case 'AddMoneyToWallet':
      return <CreditDetails />
    case 'NotificationsSetting':
      return <NotificationsSetting userData={user} />
    case 'PaymentSettings':
      return <PaymentSettings />
    case 'AccountSettings':
      return <AccountSettings />
    case 'HelpdeskSetting':
      return <HelpdeskSetting />
    case 'MyTransactions':
      return <OrderListing />
    default:
      return (
        <div className="bg-white rounded-xl p-8 shadow-sm">
          <h1 className="text-2xl font-bold mb-4">
            Welcome to your account settings
          </h1>
          <p className="text-gray-600">
            Select an option from the menu to manage your account.
          </p>
        </div>
      )
  }
}

export default function AccountSettingsTabs({
  userData,
  genres,
  authors,
}: {
  userData: any
  genres: any
  authors: any
}) {
  const { user } = useAuth()
  const searchParams = useSearchParams()
  const tabParam = searchParams.get('tab')
  const [activeTab, setActiveTab] = useState(
    tabParam || accountLinks[0].component,
  )
  useEffect(() => {
    if (tabParam && tabParam !== activeTab) {
      setActiveTab(tabParam)
    }
  }, [tabParam])

  const handleTabClick = (component: string) => {
    setActiveTab(component)
    const params = new URLSearchParams(window.location.search)
    params.set('tab', component)
    window.history.replaceState(
      {},
      '',
      `${window.location.pathname}?${params.toString()}`,
    )
    window.scrollTo(0, 0)
  }

  const accountLinkOptions = accountLinks.map((option) => ({
    value: option.component,
    label: option.title,
  }))

  return (
    <div className="flex flex-col md:flex-row justify-center gap-6 w-full p-4 md:p-8 min-h-screen bg-gray-50">
      {/* Sidebar for desktop */}
      {userData && userData?.role !== ROLES.Teacher && (
        <aside className="hidden md:flex w-80 h-max border-r bg-white rounded-xl p-6 flex-col justify-between sticky top-4">
          <div>
            <h2 className="font-bold text-lg mb-6">
              Update And Manage Your Account
            </h2>
            <nav className="flex flex-col gap-3">
              {accountLinks.map((link) => {
                const isActive = activeTab === link.component
                return (
                  <button
                    key={link.component}
                    onClick={() => handleTabClick(link.component)}
                    className={`flex items-center gap-3 px-4 py-3 rounded-lg text-left font-medium transition ${isActive ? 'bg-gray-100 text-black' : 'hover:bg-gray-50 text-gray-700'}`}
                    style={{ border: 'none' }}
                  >
                    <span className="w-6 h-6 flex items-center justify-center">
                      {link.icon(isActive)}
                    </span>
                    {link.title}
                  </button>
                )
              })}
            </nav>
          </div>
          <div className="mt-8">
            <LogoutButton />
          </div>
        </aside>
      )}

      {/* Dropdown for mobile */}
      {userData && userData?.role !== ROLES.Teacher && (
        <div className="md:hidden bg-white rounded-xl p-4 shadow-sm mb-4">
          <CustomDropDown
            icon={<Sort />}
            title={'Update And Manage Your Account'}
            options={accountLinkOptions}
            column={1}
            handleOptionClick={handleTabClick}
            selectedList={activeTab}
            isTitleType={true}
          />
        </div>
      )}

      {/* Main content */}
      <main
        className={`${userData && userData?.role === ROLES.Teacher ? 'w-full md:w-1/2' : 'flex-1'}`}
      >
        <div className="bg-white rounded-xl p-6 md:p-8 shadow-sm min-h-[calc(100vh-4rem)]">
          {getTabComponent(activeTab, user, genres, authors)}
        </div>
      </main>
    </div>
  )
}
